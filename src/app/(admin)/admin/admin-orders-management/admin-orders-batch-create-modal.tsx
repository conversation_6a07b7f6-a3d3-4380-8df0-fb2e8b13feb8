import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { createBatchOrdersDirectly } from '@/services/batch-orders-service';

interface BatchCreateFormData {
  totalOrders: number;
  buyOrdersPercentage: number;
  sellOrdersPercentage: number;
  minPrice: number;
  maxPrice: number;
}

interface AdminOrdersBatchCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => Promise<void>;
}

export function AdminOrdersBatchCreateModal({
  isOpen,
  onClose,
  onSuccess,
}: AdminOrdersBatchCreateModalProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<BatchCreateFormData>({
    totalOrders: 10,
    buyOrdersPercentage: 50,
    sellOrdersPercentage: 50,
    minPrice: 1,
    maxPrice: 10,
  });

  const handleInputChange = (
    field: keyof BatchCreateFormData,
    value: string,
  ) => {
    const numValue = parseFloat(value) || 0;
    setFormData((prev) => ({
      ...prev,
      [field]: numValue,
    }));
  };

  const validateForm = (): string | null => {
    if (formData.totalOrders <= 0) {
      return 'Total orders must be greater than 0';
    }
    if (formData.buyOrdersPercentage + formData.sellOrdersPercentage !== 100) {
      return 'Buy and sell percentages must sum to 100%';
    }
    if (formData.minPrice <= 0 || formData.maxPrice <= 0) {
      return 'Prices must be greater than 0';
    }
    if (formData.minPrice >= formData.maxPrice) {
      return 'Max price must be greater than min price';
    }
    return null;
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const result = await createBatchOrdersDirectly({
        totalOrders: formData.totalOrders,
        buyOrdersPercentage: formData.buyOrdersPercentage,
        sellOrdersPercentage: formData.sellOrdersPercentage,
        minPrice: formData.minPrice,
        maxPrice: formData.maxPrice,
      });

      toast({
        title: 'Success',
        description: result.message,
      });

      await onSuccess();
    } catch (error) {
      console.error('Error creating batch orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to create batch orders',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Admin Orders</DialogTitle>
          <DialogDescription>
            Create multiple random orders with admin accounts
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="totalOrders" className="text-right">
              Total Orders
            </Label>
            <Input
              id="totalOrders"
              type="number"
              value={formData.totalOrders}
              onChange={(e) => handleInputChange('totalOrders', e.target.value)}
              className="col-span-3"
              min="1"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="buyPercentage" className="text-right">
              Buy Orders %
            </Label>
            <Input
              id="buyPercentage"
              type="number"
              value={formData.buyOrdersPercentage}
              onChange={(e) =>
                handleInputChange('buyOrdersPercentage', e.target.value)
              }
              className="col-span-3"
              min="0"
              max="100"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sellPercentage" className="text-right">
              Sell Orders %
            </Label>
            <Input
              id="sellPercentage"
              type="number"
              value={formData.sellOrdersPercentage}
              onChange={(e) =>
                handleInputChange('sellOrdersPercentage', e.target.value)
              }
              className="col-span-3"
              min="0"
              max="100"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="minPrice" className="text-right">
              Min Price (TON)
            </Label>
            <Input
              id="minPrice"
              type="number"
              step="0.01"
              value={formData.minPrice}
              onChange={(e) => handleInputChange('minPrice', e.target.value)}
              className="col-span-3"
              min="0.01"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="maxPrice" className="text-right">
              Max Price (TON)
            </Label>
            <Input
              id="maxPrice"
              type="number"
              step="0.01"
              value={formData.maxPrice}
              onChange={(e) => handleInputChange('maxPrice', e.target.value)}
              className="col-span-3"
              min="0.01"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Creating...' : 'Create Orders'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
