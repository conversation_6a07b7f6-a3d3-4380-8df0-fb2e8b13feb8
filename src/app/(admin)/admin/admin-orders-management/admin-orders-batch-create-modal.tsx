import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { createBatchOrdersWithProgress } from '@/services/batch-orders-service';

interface BatchCreateFormData {
  totalOrders: string;
  buyOrdersPercentage: string;
  sellOrdersPercentage: string;
  minPrice: string;
  maxPrice: string;
}

interface AdminOrdersBatchCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => Promise<void>;
}

export function AdminOrdersBatchCreateModal({
  isOpen,
  onClose,
  onSuccess,
}: AdminOrdersBatchCreateModalProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [createdCount, setCreatedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [formData, setFormData] = useState<BatchCreateFormData>({
    totalOrders: '10',
    buyOrdersPercentage: '50',
    sellOrdersPercentage: '50',
    minPrice: '1',
    maxPrice: '10',
  });

  const handleInputChange = (
    field: keyof BatchCreateFormData,
    value: string,
  ) => {
    // Allow only numbers and decimal points
    const sanitizedValue = value.replace(/[^0-9.]/g, '');
    setFormData((prev) => ({
      ...prev,
      [field]: sanitizedValue,
    }));
  };

  const validateForm = (): string | null => {
    const totalOrders = parseFloat(formData.totalOrders);
    const buyPercentage = parseFloat(formData.buyOrdersPercentage);
    const sellPercentage = parseFloat(formData.sellOrdersPercentage);
    const minPrice = parseFloat(formData.minPrice);
    const maxPrice = parseFloat(formData.maxPrice);

    if (isNaN(totalOrders) || totalOrders <= 0) {
      return 'Total orders must be a valid number greater than 0';
    }
    if (isNaN(buyPercentage) || isNaN(sellPercentage)) {
      return 'Percentages must be valid numbers';
    }
    if (buyPercentage + sellPercentage !== 100) {
      return 'Buy and sell percentages must sum to 100%';
    }
    if (isNaN(minPrice) || isNaN(maxPrice) || minPrice <= 0 || maxPrice <= 0) {
      return 'Prices must be valid numbers greater than 0';
    }
    if (minPrice >= maxPrice) {
      return 'Max price must be greater than min price';
    }
    return null;
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    setProgress(0);
    setCreatedCount(0);

    const totalOrders = parseFloat(formData.totalOrders);
    setTotalCount(totalOrders);

    try {
      const result = await createBatchOrdersWithProgress(
        {
          totalOrders,
          buyOrdersPercentage: parseFloat(formData.buyOrdersPercentage),
          sellOrdersPercentage: parseFloat(formData.sellOrdersPercentage),
          minPrice: parseFloat(formData.minPrice),
          maxPrice: parseFloat(formData.maxPrice),
        },
        (created, total) => {
          setCreatedCount(created);
          setProgress((created / total) * 100);
        },
      );

      toast({
        title: 'Success',
        description: result.message,
      });

      await onSuccess();
    } catch (error) {
      console.error('Error creating batch orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to create batch orders',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setProgress(0);
      setCreatedCount(0);
      setTotalCount(0);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Admin Orders</DialogTitle>
          <DialogDescription>
            Create multiple random orders with admin accounts
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="totalOrders" className="text-right">
              Total Orders
            </Label>
            <Input
              id="totalOrders"
              type="text"
              value={formData.totalOrders}
              onChange={(e) => handleInputChange('totalOrders', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 10"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="buyPercentage" className="text-right">
              Buy Orders %
            </Label>
            <Input
              id="buyPercentage"
              type="text"
              value={formData.buyOrdersPercentage}
              onChange={(e) =>
                handleInputChange('buyOrdersPercentage', e.target.value)
              }
              className="col-span-3"
              placeholder="e.g., 50"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sellPercentage" className="text-right">
              Sell Orders %
            </Label>
            <Input
              id="sellPercentage"
              type="text"
              value={formData.sellOrdersPercentage}
              onChange={(e) =>
                handleInputChange('sellOrdersPercentage', e.target.value)
              }
              className="col-span-3"
              placeholder="e.g., 50"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="minPrice" className="text-right">
              Min Price (TON)
            </Label>
            <Input
              id="minPrice"
              type="text"
              value={formData.minPrice}
              onChange={(e) => handleInputChange('minPrice', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 1.0"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="maxPrice" className="text-right">
              Max Price (TON)
            </Label>
            <Input
              id="maxPrice"
              type="text"
              value={formData.maxPrice}
              onChange={(e) => handleInputChange('maxPrice', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 10.0"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Creating...' : 'Create Orders'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
