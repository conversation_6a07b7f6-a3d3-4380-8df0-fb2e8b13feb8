import { Trash2, <PERSON><PERSON><PERSON><PERSON> } from 'lucide-react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { OrderEntity, UserEntity } from '@/constants/core.constants';
import { OrderStatus, UserType } from '@/constants/core.constants';

interface AdminOrdersTableProps {
  orders: OrderEntity[];
  userCache: Record<string, UserEntity>;
  onCancelOrder: (orderId: string) => Promise<void>;
  onDeleteOrder: (orderId: string) => Promise<void>;
}

function getAdminUserType(
  order: OrderEntity,
  userCache: Record<string, UserEntity>,
): UserType | null {
  const buyerIsAdmin =
    order.buyerId && userCache[order.buyerId]?.role === 'admin';
  const sellerIsAdmin =
    order.sellerId && userCache[order.sellerId]?.role === 'admin';

  if (buyerIsAdmin && sellerIsAdmin) {
    return UserType.BUYER;
  }
  if (buyerIsAdmin) return UserType.BUYER;
  if (sellerIsAdmin) return UserType.SELLER;
  return null;
}

function formatPrice(price: number): string {
  return `${price.toFixed(2)} TON`;
}

function getStatusBadgeClass(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.ACTIVE:
      return 'bg-blue-100 text-blue-800';
    case OrderStatus.PAID:
      return 'bg-green-100 text-green-800';
    case OrderStatus.GIFT_SENT_TO_RELAYER:
      return 'bg-purple-100 text-purple-800';
    case OrderStatus.FULFILLED:
      return 'bg-emerald-100 text-emerald-800';
    default:
      return 'bg-red-100 text-red-800';
  }
}

export function AdminOrdersTable({
  orders,
  userCache,
  onCancelOrder,
  onDeleteOrder,
}: AdminOrdersTableProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order Number</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Admin Type</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Secondary Price</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => {
            const adminType = getAdminUserType(order, userCache);
            return (
              <TableRow key={order.id}>
                <TableCell className="font-mono">#{order.number}</TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      order.status,
                    )}`}
                  >
                    {order.status}
                  </span>
                </TableCell>
                <TableCell>{adminType || 'N/A'}</TableCell>
                <TableCell>{formatPrice(order.price)}</TableCell>
                <TableCell>
                  {order.secondaryMarketPrice
                    ? formatPrice(order.secondaryMarketPrice)
                    : '-'}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    {order.status === OrderStatus.CANCELLED ? (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                            Delete
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Order</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to permanently delete order
                              #{order.number}? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => onDeleteOrder(order.id!)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    ) : (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <XCircle className="h-4 w-4" />
                            Cancel
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Cancel Order</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to cancel order #
                              {order.number}? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => onCancelOrder(order.id!)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Cancel Order
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
